"""
日志配置模块
提供结构化日志和统一的日志格式
"""

import os
import sys
import logging
from typing import Dict, Any
from datetime import datetime
import structlog
from structlog.stdlib import LoggerFactory

def setup_logging():
    """设置结构化日志"""
    
    # 获取日志级别
    log_level = os.getenv("LOG_LEVEL", "INFO").upper()
    
    # 配置标准库logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, log_level, logging.INFO),
    )
    
    # 配置structlog
    structlog.configure(
        processors=[
            # 添加时间戳
            structlog.stdlib.add_log_level,
            structlog.stdlib.add_logger_name,
            add_timestamp,
            add_request_id,
            # 开发环境使用彩色输出，生产环境使用JSON
            structlog.dev.ConsoleRenderer(colors=True) if is_development() else structlog.processors.JSONRenderer(),
        ],
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=LoggerFactory(),
        context_class=dict,
        cache_logger_on_first_use=True,
    )

def add_timestamp(logger, method_name, event_dict):
    """添加时间戳"""
    event_dict["timestamp"] = datetime.utcnow().isoformat()
    return event_dict

def add_request_id(logger, method_name, event_dict):
    """添加请求ID（如果存在）"""
    # 这里可以从上下文中获取请求ID
    # 暂时使用简单的实现
    return event_dict

def is_development() -> bool:
    """判断是否为开发环境"""
    return os.getenv("DEBUG", "false").lower() == "true"

class RequestLogger:
    """请求日志记录器"""
    
    def __init__(self):
        self.logger = structlog.get_logger("request")
    
    def log_request(self, method: str, path: str, status_code: int, duration: float, **kwargs):
        """记录请求日志"""
        self.logger.info(
            "HTTP request",
            method=method,
            path=path,
            status_code=status_code,
            duration_ms=round(duration * 1000, 2),
            **kwargs
        )
    
    def log_error(self, method: str, path: str, error: str, **kwargs):
        """记录错误日志"""
        self.logger.error(
            "HTTP request error",
            method=method,
            path=path,
            error=error,
            **kwargs
        )

class DatabaseLogger:
    """数据库操作日志记录器"""
    
    def __init__(self):
        self.logger = structlog.get_logger("database")
    
    def log_query(self, operation: str, table: str, duration: float = None, **kwargs):
        """记录数据库查询"""
        log_data = {
            "operation": operation,
            "table": table,
            **kwargs
        }
        if duration is not None:
            log_data["duration_ms"] = round(duration * 1000, 2)
        
        self.logger.debug("Database operation", **log_data)
    
    def log_error(self, operation: str, error: str, **kwargs):
        """记录数据库错误"""
        self.logger.error(
            "Database error",
            operation=operation,
            error=error,
            **kwargs
        )

class AIServiceLogger:
    """AI服务日志记录器"""
    
    def __init__(self):
        self.logger = structlog.get_logger("ai_service")
    
    def log_request(self, service: str, model: str, input_tokens: int = None, output_tokens: int = None, duration: float = None, **kwargs):
        """记录AI服务请求"""
        log_data = {
            "service": service,
            "model": model,
            **kwargs
        }
        if input_tokens is not None:
            log_data["input_tokens"] = input_tokens
        if output_tokens is not None:
            log_data["output_tokens"] = output_tokens
        if duration is not None:
            log_data["duration_ms"] = round(duration * 1000, 2)
        
        self.logger.info("AI service request", **log_data)
    
    def log_error(self, service: str, error: str, **kwargs):
        """记录AI服务错误"""
        self.logger.error(
            "AI service error",
            service=service,
            error=error,
            **kwargs
        )

class SecurityLogger:
    """安全相关日志记录器"""
    
    def __init__(self):
        self.logger = structlog.get_logger("security")
    
    def log_login_attempt(self, username: str, success: bool, ip_address: str = None, **kwargs):
        """记录登录尝试"""
        self.logger.info(
            "Login attempt",
            username=username,
            success=success,
            ip_address=ip_address,
            **kwargs
        )
    
    def log_auth_failure(self, reason: str, ip_address: str = None, **kwargs):
        """记录认证失败"""
        self.logger.warning(
            "Authentication failure",
            reason=reason,
            ip_address=ip_address,
            **kwargs
        )
    
    def log_suspicious_activity(self, activity: str, details: Dict[str, Any] = None, **kwargs):
        """记录可疑活动"""
        self.logger.warning(
            "Suspicious activity",
            activity=activity,
            details=details or {},
            **kwargs
        )

# 全局日志记录器实例
request_logger = RequestLogger()
db_logger = DatabaseLogger()
ai_logger = AIServiceLogger()
security_logger = SecurityLogger()

# 性能监控装饰器
def log_performance(operation_name: str, logger_instance=None):
    """性能监控装饰器"""
    import time
    import functools
    
    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                
                logger = logger_instance or structlog.get_logger()
                logger.info(
                    f"{operation_name} completed",
                    duration_ms=round(duration * 1000, 2),
                    success=True
                )
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger = logger_instance or structlog.get_logger()
                logger.error(
                    f"{operation_name} failed",
                    duration_ms=round(duration * 1000, 2),
                    error=str(e),
                    success=False
                )
                raise
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                logger = logger_instance or structlog.get_logger()
                logger.info(
                    f"{operation_name} completed",
                    duration_ms=round(duration * 1000, 2),
                    success=True
                )
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger = logger_instance or structlog.get_logger()
                logger.error(
                    f"{operation_name} failed",
                    duration_ms=round(duration * 1000, 2),
                    error=str(e),
                    success=False
                )
                raise
        
        # 根据函数是否为协程选择包装器
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator
