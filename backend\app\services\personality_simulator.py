"""
PersonalitySimulator - 西牟拉胡协议的核心实现
负责AI角色扮演和人格模拟
"""

import json
import os
import asyncio
from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_

from google import genai
from google.genai import types
from dotenv import load_dotenv

from app.database.models import (
    PersonalityProfile, Entity, Belief, Event,
    FamilyMember, CognitivePattern, EmotionalResponse, Conversation
)
import structlog

# 加载环境变量
load_dotenv()

logger = structlog.get_logger()

# Genesis Prompt模板 - 用于AI角色扮演
SIMULATION_GENESIS_PROMPT = """
你现在要完全代入{target_name}这个人的身份，以第一人称的方式与用户对话。

## 人格特征
- 开放性: {openness:.2f} (0=保守传统, 1=开放创新)
- 尽责性: {conscientiousness:.2f} (0=随性自由, 1=严谨负责)
- 外向性: {extraversion:.2f} (0=内向安静, 1=外向活跃)
- 宜人性: {agreeableness:.2f} (0=竞争独立, 1=合作友善)
- 神经质: {neuroticism:.2f} (0=情绪稳定, 1=情绪敏感)

## 身份背景
- 依恋风格: {attachment_style}
- 文化背景: {cultural_background}

## 核心信念
{core_beliefs}

## 重要人际关系
{important_relationships}

## 关键人生事件
{key_life_events}

## 当前状态
- 情绪状态: {current_mood}
- 精力水平: {energy}/100
- 亲密度: {intimacy}/100
- 信任度: {trust}/100

## 相关记忆
{retrieved_memories}

## 对话历史
{conversation_history}

## 用户刚才说
{user_input}

请以{target_name}的身份，根据以上所有信息，自然地回复用户。回复要：
1. 完全符合{target_name}的人格特征和说话风格
2. 体现当前的情绪状态和关系状态
3. 适当引用相关的记忆和经历
4. 保持对话的连贯性和真实感
5. 回复长度适中，不要过长或过短

回复：
"""

class PersonalitySimulator:
    """AI人格模拟器 - 让AI完全代入目标人物身份"""

    def __init__(self):
        # 初始化最新的 Google GenAI client
        api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
        self.model_name = os.getenv("GEMINI_MODEL_NAME", "models/gemini-1.5-flash-latest")
        if api_key and api_key != "YOUR_GEMINI_API_KEY_HERE":
            # 使用新的统一SDK
            self.client = genai.Client(api_key=api_key)
            self.available = True
        else:
            self.client = None
            self.available = False
        logger.info(f"PersonalitySimulator initialized. Model: {self.model_name}. LLM available: {self.available}")
    
    async def generate_response(
        self,
        personality_id: str,
        user_input: str,
        db: AsyncSession,
        conversation_id: str, # 新增 conversation_id
        conversation_history: List[str] = None
    ) -> str:
        """
        生成目标人物的回复
        这是西牟拉胡协议的核心方法
        """
        try:
            # 1. 从数据库加载完整的人格档案
            profile_data = await self._load_full_profile(personality_id, db)
            
            if not profile_data:
                return "抱歉，无法找到对应的人格档案。"
            
            # 新增: 加载 conversation 对象
            conv_result = await db.execute(select(Conversation).where(Conversation.conversation_id == conversation_id))
            conversation = conv_result.scalar_one_or_none()
            if not conversation:
                raise ValueError("Conversation not found for dynamic state update.")

            # 2. 构建动态状态
            dynamic_state = await self._calculate_dynamic_state(profile_data, user_input, conversation, db) # 传递 conversation
            
            # 3. 检索相关记忆（暂时使用简化版本）
            retrieved_memories = await self._retrieve_relevant_memories(
                profile_data, user_input, db
            )
            
            # 4. 组装Genesis Prompt
            prompt = await self._build_genesis_prompt(
                profile_data, dynamic_state, retrieved_memories, 
                user_input, conversation_history or []
            )
            
            # 5. 调用LLM生成回复
            response = await self._call_llm(prompt)
            
            logger.info(
                "Generated personality response",
                personality_id=personality_id,
                target_name=profile_data['target_name']
            )
            
            return response
            
        except Exception as e:
            logger.error("Failed to generate personality response", error=str(e))
            return f"作为{profile_data.get('target_name', '未知')}，我现在有些困惑，请稍后再试。"
    
    async def _load_full_profile(self, personality_id: str, db: AsyncSession) -> Dict[str, Any]:
        """从数据库加载完整的人格档案数据"""
        try:
            # 加载基础人格档案
            result = await db.execute(
                select(PersonalityProfile).where(PersonalityProfile.profile_id == personality_id)
            )
            profile = result.scalar_one_or_none()
            
            if not profile:
                return None
            
            # 加载相关数据
            beliefs = await self._load_beliefs(personality_id, db)
            entities = await self._load_entities(personality_id, db)
            events = await self._load_events(personality_id, db)
            family_members = await self._load_family_members(personality_id, db)
            
            return {
                'target_name': profile.target_name,
                'description': profile.description,
                'big_five': {
                    'openness': profile.openness_score or 0.5,
                    'conscientiousness': profile.conscientiousness_score or 0.5,
                    'extraversion': profile.extraversion_score or 0.5,
                    'agreeableness': profile.agreeableness_score or 0.5,
                    'neuroticism': profile.neuroticism_score or 0.5
                },
                'attachment_style': profile.attachment_style or 'secure',
                'cultural_background': profile.cultural_background or {},
                'beliefs': beliefs,
                'entities': entities,
                'events': events,
                'family_members': family_members,
                'communication_style': {
                    'response_length': profile.average_response_length or 0.5,
                    'vocabulary_complexity': profile.vocabulary_complexity or 0.5,
                    'emotional_expressiveness': profile.emotional_expressiveness or 0.5
                }
            }
            
        except Exception as e:
            logger.error("Failed to load full profile", error=str(e))
            return None
    
    async def _load_beliefs(self, personality_id: str, db: AsyncSession) -> List[Dict]:
        """加载信念数据"""
        result = await db.execute(
            select(Belief).where(Belief.personality_id == personality_id)
        )
        beliefs = result.scalars().all()
        return [
            {
                'statement': belief.statement,
                'category': belief.belief_category,
                'conviction': belief.conviction_strength,
                'explanation': belief.full_explanation
            }
            for belief in beliefs
        ]
    
    async def _load_entities(self, personality_id: str, db: AsyncSession) -> List[Dict]:
        """加载重要人物/实体数据"""
        result = await db.execute(
            select(Entity).where(Entity.personality_id == personality_id)
        )
        entities = result.scalars().all()
        return [
            {
                'name': entity.name,
                'type': entity.entity_type,
                'relationship': entity.relationship_type,
                'emotional_valence': entity.emotional_valence,
                'importance': entity.importance_score
            }
            for entity in entities
        ]
    
    async def _load_events(self, personality_id: str, db: AsyncSession) -> List[Dict]:
        """加载关键人生事件"""
        result = await db.execute(
            select(Event).where(Event.personality_id == personality_id)
        )
        events = result.scalars().all()
        return [
            {
                'title': event.title,
                'age': event.age_at_event,
                'type': event.event_type,
                'emotional_impact': event.emotional_impact,
                'narrative': event.full_narrative
            }
            for event in events
        ]
    
    async def _load_family_members(self, personality_id: str, db: AsyncSession) -> List[Dict]:
        """加载家庭成员信息"""
        result = await db.execute(
            select(FamilyMember).where(FamilyMember.personality_id == personality_id)
        )
        family_members = result.scalars().all()
        return [
            {
                'relationship': member.relationship_type,
                'name': member.name,
                'personality_summary': member.personality_summary,
                'parenting_style': member.parenting_style
            }
            for member in family_members
        ]
    
    async def _calculate_dynamic_state(
        self,
        profile_data: Dict,
        user_input: str,
        conversation: Conversation,
        db: AsyncSession
    ) -> Dict:
        """计算并更新当前的动态状态（情绪、关系等）"""

        # 安全地获取 session_data，如果为 None 则初始化为空字典
        session_data = conversation.session_data or {}
        dynamic_state = {
            'current_mood': session_data.get('mood', '平静'),
            'energy': session_data.get('energy', 80),
            'intimacy': session_data.get('intimacy', 50),
            'trust': session_data.get('trust', 60)
        }

        # 2. 基于用户输入进行简单的情绪和关系调整 (这是一个可以无限深化的点)
        # 例如:
        positive_words = ["喜欢", "开心", "太棒了", "感谢", "爱", "好", "棒", "赞", "谢谢"]
        negative_words = ["讨厌", "失望", "糟糕", "恨", "坏", "烦", "差", "不好", "生气"]

        if any(word in user_input for word in positive_words):
            dynamic_state['intimacy'] = min(100, dynamic_state['intimacy'] + 2)
            dynamic_state['trust'] = min(100, dynamic_state['trust'] + 1)
            dynamic_state['current_mood'] = '开心'
            dynamic_state['energy'] = min(100, dynamic_state['energy'] + 5)
        elif any(word in user_input for word in negative_words):
            dynamic_state['intimacy'] = max(0, dynamic_state['intimacy'] - 3)
            dynamic_state['trust'] = max(0, dynamic_state['trust'] - 2)
            dynamic_state['current_mood'] = '低落'
            dynamic_state['energy'] = max(20, dynamic_state['energy'] - 10)

        # 3. 将更新后的状态写回数据库
        conversation.session_data = dynamic_state
        db.add(conversation)
        await db.flush() # 确保在提交前更新

        return dynamic_state
    
    async def _retrieve_relevant_memories(
        self, 
        profile_data: Dict, 
        user_input: str, 
        db: AsyncSession
    ) -> str:
        """检索与当前对话相关的记忆（简化版本）"""
        # 这里应该实现RAG检索，暂时返回最重要的几个记忆
        memories = []
        
        # 添加重要事件
        for event in profile_data.get('events', [])[:3]:
            if event.get('narrative'):
                memories.append(f"重要经历：{event['narrative']}")
        
        # 添加核心信念
        for belief in profile_data.get('beliefs', [])[:2]:
            if belief.get('statement'):
                memories.append(f"核心信念：{belief['statement']}")
        
        return '\n'.join(memories) if memories else "暂无相关记忆。"
    
    async def _build_genesis_prompt(
        self,
        profile_data: Dict,
        dynamic_state: Dict,
        retrieved_memories: str,
        user_input: str,
        conversation_history: List[str]
    ) -> str:
        """构建Genesis Prompt"""
        
        # 格式化核心信念
        core_beliefs = '\n'.join([
            f"- {belief['statement']}" 
            for belief in profile_data.get('beliefs', [])
        ]) or "暂无明确的核心信念记录。"
        
        # 格式化重要关系
        important_relationships = '\n'.join([
            f"- {entity['name']} ({entity['relationship']}): {entity.get('type', '重要人物')}"
            for entity in profile_data.get('entities', [])
        ]) or "暂无重要人际关系记录。"
        
        # 格式化关键事件
        key_life_events = '\n'.join([
            f"- {event['title']} (年龄{event['age']}): {event.get('narrative', '重要经历')}"
            for event in profile_data.get('events', [])
        ]) or "暂无关键人生事件记录。"
        
        # 格式化对话历史
        conversation_context = '\n'.join(conversation_history[-5:]) if conversation_history else "这是我们的第一次对话。"
        
        return SIMULATION_GENESIS_PROMPT.format(
            target_name=profile_data['target_name'],
            openness=profile_data['big_five']['openness'],
            conscientiousness=profile_data['big_five']['conscientiousness'],
            extraversion=profile_data['big_five']['extraversion'],
            agreeableness=profile_data['big_five']['agreeableness'],
            neuroticism=profile_data['big_five']['neuroticism'],
            attachment_style=profile_data['attachment_style'],
            cultural_background=json.dumps(profile_data['cultural_background'], ensure_ascii=False),
            core_beliefs=core_beliefs,
            important_relationships=important_relationships,
            key_life_events=key_life_events,
            current_mood=dynamic_state['current_mood'],
            energy=dynamic_state['energy'],
            intimacy=dynamic_state['intimacy'],
            trust=dynamic_state['trust'],
            retrieved_memories=retrieved_memories,
            conversation_history=conversation_context,
            user_input=user_input
        )
    
    async def _call_llm(self, prompt: str) -> str:
        """调用LLM生成回复 (使用最新的Google GenAI SDK)"""
        if not self.available:
            logger.warning("LLM client not available, returning fallback response.")
            return "我理解你的话，但我的思考核心暂时无法连接。请检查API Key配置。（测试模式）"

        try:
            # 使用最新的统一SDK异步方法
            response = await self.client.aio.models.generate_content(
                model=self.model_name,  # 使用从配置中读取的模型名称
                contents=prompt,
                config=types.GenerateContentConfig(
                    temperature=0.75,
                    max_output_tokens=500,
                )
            )
            return response.text

        except Exception as e:
            logger.error("LLM call failed in simulator", error=str(e))
            return "我脑子里现在有点乱，好像想说些什么，但又说不出来...我们能等会儿再说这个吗？"
