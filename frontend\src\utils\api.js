import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 添加请求时间戳（防止缓存）
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }

    // 显示加载状态
    if (config.showLoading !== false) {
      // 这里可以显示全局loading
    }

    console.log('发送请求:', config.method?.toUpperCase(), config.url, config.data || config.params)
    
    return config
  },
  (error) => {
    console.error('请求配置错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 隐藏加载状态
    if (response.config.showLoading !== false) {
      // 这里可以隐藏全局loading
    }

    console.log('收到响应:', response.status, response.config.url, response.data)
    
    return response
  },
  async (error) => {
    // 隐藏加载状态
    if (error.config?.showLoading !== false) {
      // 这里可以隐藏全局loading
    }

    const { response, config } = error

    // 网络错误
    if (!response) {
      ElMessage.error('网络连接失败，请检查网络设置')
      return Promise.reject(error)
    }

    const { status, data } = response

    // 根据状态码处理不同错误
    switch (status) {
      case 400:
        ElMessage.error(data?.detail || '请求参数错误')
        break
        
      case 401:
        // 未授权，清除token并跳转到登录页
        localStorage.removeItem('token')
        localStorage.removeItem('user')
        delete api.defaults.headers.common['Authorization']
        
        ElMessageBox.confirm(
          '登录状态已过期，请重新登录',
          '提示',
          {
            confirmButtonText: '重新登录',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
          window.location.href = '/login'
        }).catch(() => {
          // 用户取消
        })
        break
        
      case 403:
        ElMessage.error('没有权限访问该资源')
        break
        
      case 404:
        ElMessage.error('请求的资源不存在')
        break
        
      case 422:
        // 验证错误
        if (data?.detail && Array.isArray(data.detail)) {
          const errors = data.detail.map(err => err.msg).join(', ')
          ElMessage.error(`数据验证失败: ${errors}`)
        } else {
          ElMessage.error(data?.detail || '数据验证失败')
        }
        break
        
      case 429:
        ElMessage.error('请求过于频繁，请稍后再试')
        break
        
      case 500:
        ElMessage.error('服务器内部错误，请稍后重试')
        break
        
      case 502:
      case 503:
      case 504:
        ElMessage.error('服务暂时不可用，请稍后重试')
        break
        
      default:
        ElMessage.error(data?.detail || `请求失败 (${status})`)
    }

    console.error('请求失败:', status, config?.url, data)
    
    return Promise.reject(error)
  }
)

// API方法封装
export const apiMethods = {
  // 认证相关
  auth: {
    login: (credentials) => api.post('/auth/login', credentials),
    register: (userData) => api.post('/auth/register', userData),
    logout: () => api.post('/auth/logout'),
    refreshToken: () => api.post('/auth/refresh')
  },

  // 人格档案相关
  personalities: {
    list: () => api.get('/personalities'),
    create: (data) => api.post('/personalities', data),
    get: (id) => api.get(`/personalities/${id}`),
    update: (id, data) => api.put(`/personalities/${id}`, data),
    delete: (id) => api.delete(`/personalities/${id}`)
  },

  // 对话相关
  chat: {
    start: (personalityId) => api.post(`/chat/start/${personalityId}`),
    respond: (data) => api.post('/chat/respond', data),
    getHistory: (conversationId) => api.get(`/chat/history/${conversationId}`),
    getConversations: (personalityId) => api.get(`/chat/conversations/${personalityId}`)
  },

  // 预测相关
  prediction: {
    predict: (data) => api.post('/predict', data),
    getHistory: (personalityId) => api.get(`/predictions/${personalityId}`)
  },

  // 验证相关
  validation: {
    validate: (data) => api.post('/validate', data),
    getReports: (personalityId) => api.get(`/validation/reports/${personalityId}`)
  },

  // 分析相关
  analytics: {
    getOverview: () => api.get('/analytics/overview'),
    getPersonalityStats: (personalityId) => api.get(`/analytics/personality/${personalityId}`),
    getProgressReport: (personalityId) => api.get(`/analytics/progress/${personalityId}`)
  },

  // 模拟相关 - 西牟拉胡协议
  simulation: {
    start: (personalityId, initialMessage = '你好') =>
      api.post(`/api/v1/simulation/start/${personalityId}`, {
        personality_id: personalityId,
        initial_message: initialMessage
      }),
    chat: (conversationId, userInput) =>
      api.post(`/api/v1/simulation/chat/${conversationId}`, { user_input: userInput }),
    getConversations: (personalityId) =>
      api.get(`/api/v1/simulation/conversations/${personalityId}`),
    getMessages: (conversationId) =>
      api.get(`/api/v1/simulation/messages/${conversationId}`),
    passTime: (data) =>
      api.post('/api/v1/simulation/pass-time', data)
  }
}

// 文件上传
export const uploadFile = (file, onProgress) => {
  const formData = new FormData()
  formData.append('file', file)
  
  return api.post('/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(progress)
      }
    }
  })
}

// 下载文件
export const downloadFile = (url, filename) => {
  return api.get(url, {
    responseType: 'blob'
  }).then(response => {
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  })
}

// 批量请求
export const batchRequest = (requests) => {
  return Promise.allSettled(requests.map(request => api(request)))
}

// 重试机制
export const retryRequest = async (requestFn, maxRetries = 3, delay = 1000) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await requestFn()
    } catch (error) {
      if (i === maxRetries - 1) throw error
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
    }
  }
}

export default api
