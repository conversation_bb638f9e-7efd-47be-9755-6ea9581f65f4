#!/usr/bin/env python3
"""
RAG记忆检索功能测试脚本
用于验证语义相似度检索是否正常工作
"""

import asyncio
import sys
import os
from typing import Dict, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.personality_simulator import PersonalitySimulator
from app.database.db_session import get_db_session
import structlog

logger = structlog.get_logger()

async def test_rag_functionality():
    """测试RAG记忆检索功能"""
    print("🧠 测试RAG记忆检索功能")
    print("=" * 50)
    
    # 创建模拟器实例
    simulator = PersonalitySimulator()
    
    if not simulator.rag_model:
        print("❌ RAG模型未加载，请检查sentence-transformers安装")
        return False
    
    print("✅ RAG模型加载成功")
    
    # 创建测试用的人格数据
    test_profile_data = {
        'target_name': '张小明',
        'events': [
            {
                'title': '大学毕业典礼',
                'age': 22,
                'narrative': '那天我穿着学士服，和室友们一起拍照留念。虽然对未来有些不安，但更多的是兴奋和期待。'
            },
            {
                'title': '第一次失恋',
                'age': 20,
                'narrative': '和高中同学分手了，当时觉得天都塌了。在宿舍里哭了一整夜，室友陪着我吃了很多冰淇淋。'
            },
            {
                'title': '学会骑自行车',
                'age': 8,
                'narrative': '爸爸在公园里教我骑车，摔了好多次，膝盖都破皮了。但是学会的那一刻，感觉自己像飞起来一样。'
            }
        ],
        'beliefs': [
            {
                'statement': '努力总会有回报',
                'explanation': '从小到大的经历让我相信，只要坚持不懈地努力，最终都会看到成果。'
            },
            {
                'statement': '友谊比爱情更持久',
                'explanation': '经历过几次感情波折后，我发现真正的朋友会一直陪伴在身边。'
            }
        ],
        'entities': [
            {
                'name': '小红',
                'relationship': '最好的朋友',
                'description': '从小学就认识的闺蜜，无话不谈，总是在我需要的时候出现。'
            }
        ]
    }
    
    # 测试不同类型的查询
    test_queries = [
        "你觉得努力重要吗？",
        "你有什么难忘的童年回忆？",
        "你对友谊怎么看？",
        "你上大学的时候怎么样？",
        "你会骑自行车吗？"
    ]
    
    print("\n🔍 开始测试记忆检索...")
    
    async with get_db_session() as db:
        for i, query in enumerate(test_queries, 1):
            print(f"\n--- 测试 {i}: {query} ---")
            
            try:
                retrieved_memories = await simulator._retrieve_relevant_memories(
                    test_profile_data, query, db, top_k=3
                )
                
                print("检索结果:")
                print(retrieved_memories)
                
            except Exception as e:
                print(f"❌ 检索失败: {e}")
    
    print("\n✅ RAG功能测试完成")
    return True

async def main():
    """主函数"""
    try:
        success = await test_rag_functionality()
        return 0 if success else 1
    except Exception as e:
        logger.error("Test failed", error=str(e))
        print(f"❌ 测试失败: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
