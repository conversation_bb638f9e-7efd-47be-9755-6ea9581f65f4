#!/usr/bin/env python3
"""
批量角色生成脚本
一次性生成多个不同类型的角色，用于快速填充数据库
"""

import asyncio
import sys
import os
from typing import List, Dict, Any
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from character_generator import CharacterGenerator
from app.database.db_session import init_database, get_db_session
from app.database.models import User
from app.auth_utils import get_password_hash
from sqlalchemy.future import select
import structlog

logger = structlog.get_logger()

# 预定义的角色类型和背景
CHARACTER_TEMPLATES = [
    {
        "type": "科技公司程序员",
        "context": "25-30岁，在一线城市工作，热爱编程和技术，有一定的社交焦虑，但在专业领域很自信。喜欢游戏、动漫，追求工作与生活的平衡。"
    },
    {
        "type": "文艺青年",
        "context": "22-28岁，喜欢文学、电影、音乐，有艺术追求，情感丰富，思想深刻。可能从事创意相关工作，对物质要求不高，更注重精神追求。"
    },
    {
        "type": "职场白领",
        "context": "28-35岁，在大公司工作，有一定的职业规划和野心，注重形象和人际关系，生活节奏较快，压力较大。"
    },
    {
        "type": "大学生",
        "context": "18-22岁，正在接受高等教育，对未来充满憧憬和不确定性，活跃在社交媒体，关注流行文化，有理想主义倾向。"
    },
    {
        "type": "创业者",
        "context": "25-40岁，有创业经历或正在创业，风险承受能力强，目标导向，善于沟通和说服他人，工作狂倾向。"
    },
    {
        "type": "教师",
        "context": "25-45岁，从事教育工作，有耐心和责任心，关心他人成长，知识面广，有一定的理想主义色彩。"
    },
    {
        "type": "医护人员",
        "context": "25-40岁，从事医疗相关工作，有强烈的责任感和同理心，工作压力大，但有使命感，关注健康和生命。"
    },
    {
        "type": "艺术家",
        "context": "20-50岁，从事艺术创作，个性鲜明，情感表达丰富，对美有独特见解，可能经济状况不稳定但精神富足。"
    },
    {
        "type": "退休老人",
        "context": "60-75岁，已退休，有丰富的人生阅历，关注健康和家庭，可能有一些传统观念，但也在适应新时代。"
    },
    {
        "type": "家庭主妇",
        "context": "25-45岁，以家庭为重心，有育儿经验，善于管理家务，可能有一些个人兴趣爱好，关注家人健康和教育。"
    }
]

class BatchCharacterGenerator:
    """批量角色生成器"""
    
    def __init__(self):
        self.generator = CharacterGenerator()
        self.generated_characters = []
    
    async def ensure_demo_user_exists(self) -> str:
        """确保演示用户存在"""
        try:
            async with get_db_session() as db:
                # 查找演示用户
                result = await db.execute(
                    select(User).where(User.username == "demo")
                )
                user = result.scalar_one_or_none()
                
                if not user:
                    # 创建演示用户，使用安全的密码哈希
                    user = User(
                        username="demo",
                        email="<EMAIL>",
                        hashed_password=get_password_hash("demo123")  # 使用安全的密码哈希
                    )
                    db.add(user)
                    await db.commit()
                    await db.refresh(user)
                    logger.info("Created demo user", user_id=str(user.user_id))
                
                return str(user.user_id)
                
        except Exception as e:
            logger.error("Failed to ensure demo user exists", error=str(e))
            raise
    
    async def generate_character_batch(
        self, 
        templates: List[Dict[str, str]] = None,
        user_id: str = None
    ) -> List[Dict[str, Any]]:
        """批量生成角色"""
        if templates is None:
            templates = CHARACTER_TEMPLATES
        
        if user_id is None:
            user_id = await self.ensure_demo_user_exists()
        
        results = []
        
        for i, template in enumerate(templates, 1):
            try:
                print(f"\n🎭 正在生成角色 {i}/{len(templates)}: {template['type']}")
                
                # 生成角色
                character = await self.generator.generate_character(
                    character_type=template['type'],
                    additional_context=template['context']
                )
                
                # 保存到数据库
                personality_id = await self.generator.save_character_to_db(character, user_id)
                
                result = {
                    "template": template,
                    "character": character,
                    "personality_id": personality_id,
                    "status": "success"
                }
                
                results.append(result)
                self.generated_characters.append(result)
                
                print(f"✅ 成功生成: {character.target_name} (ID: {personality_id})")
                
                # 添加延迟以避免API限制
                await asyncio.sleep(2)
                
            except Exception as e:
                logger.error(
                    "Failed to generate character",
                    template_type=template['type'],
                    error=str(e)
                )
                
                result = {
                    "template": template,
                    "character": None,
                    "personality_id": None,
                    "status": "failed",
                    "error": str(e)
                }
                
                results.append(result)
                print(f"❌ 生成失败: {template['type']} - {e}")
        
        return results
    
    def print_summary(self, results: List[Dict[str, Any]]):
        """打印生成摘要"""
        successful = [r for r in results if r["status"] == "success"]
        failed = [r for r in results if r["status"] == "failed"]
        
        print(f"\n{'='*60}")
        print(f"📊 批量生成完成摘要")
        print(f"{'='*60}")
        print(f"总计: {len(results)} 个角色")
        print(f"成功: {len(successful)} 个")
        print(f"失败: {len(failed)} 个")
        
        if successful:
            print(f"\n✅ 成功生成的角色:")
            for result in successful:
                char = result["character"]
                print(f"   • {char.target_name} ({char.occupation}) - ID: {result['personality_id']}")
        
        if failed:
            print(f"\n❌ 生成失败的角色:")
            for result in failed:
                print(f"   • {result['template']['type']} - 错误: {result['error']}")
    
    async def generate_custom_characters(self, custom_templates: List[Dict[str, str]]):
        """生成自定义角色列表"""
        print(f"🎯 开始生成 {len(custom_templates)} 个自定义角色...")
        
        user_id = await self.ensure_demo_user_exists()
        results = await self.generate_character_batch(custom_templates, user_id)
        
        self.print_summary(results)
        return results

async def main():
    """主函数"""
    print("🎭 批量角色数据生成器")
    print("=" * 60)
    
    # 初始化数据库
    await init_database()
    
    batch_generator = BatchCharacterGenerator()
    
    print("请选择生成模式:")
    print("1. 生成所有预定义角色类型")
    print("2. 生成指定数量的随机角色")
    print("3. 自定义角色类型")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    try:
        if choice == "1":
            # 生成所有预定义角色
            print(f"\n🚀 开始生成 {len(CHARACTER_TEMPLATES)} 个预定义角色...")
            results = await batch_generator.generate_character_batch()
            batch_generator.print_summary(results)
            
        elif choice == "2":
            # 生成指定数量的随机角色
            count = int(input("请输入要生成的角色数量: "))
            if count > len(CHARACTER_TEMPLATES):
                print(f"⚠️  最多只能生成 {len(CHARACTER_TEMPLATES)} 个角色")
                count = len(CHARACTER_TEMPLATES)
            
            selected_templates = CHARACTER_TEMPLATES[:count]
            print(f"\n🚀 开始生成 {count} 个随机角色...")
            results = await batch_generator.generate_character_batch(selected_templates)
            batch_generator.print_summary(results)
            
        elif choice == "3":
            # 自定义角色类型
            custom_templates = []
            print("\n请输入自定义角色信息 (输入空行结束):")
            
            while True:
                char_type = input("角色类型: ").strip()
                if not char_type:
                    break
                
                context = input("背景描述: ").strip()
                custom_templates.append({
                    "type": char_type,
                    "context": context
                })
                print("✅ 已添加角色类型\n")
            
            if custom_templates:
                results = await batch_generator.generate_custom_characters(custom_templates)
            else:
                print("❌ 没有输入任何角色类型")
                return 1
        
        else:
            print("❌ 无效选择")
            return 1
        
        print(f"\n🎉 批量生成完成!")
        
    except KeyboardInterrupt:
        print(f"\n⚠️  用户中断操作")
        return 1
    except Exception as e:
        print(f"❌ 生成过程中出现错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
