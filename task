继续执行新的任务：

好的，没有问题。实现“继续历史对话”和“实现RAG记忆检索”是让你的项目从一个有趣的演示变为一个真正可用的、有深度的AI交互系统的关键。

下面我将为你提供完整的、可直接使用的代码和详细的步骤。

---

### 第1部分：实现RAG记忆检索 (后端)

我们将使用 `sentence-transformers` 库来实现一个轻量级的、高效的RAG系统。这不需要外部的向量数据库，对于当前项目规模来说是最佳选择。

#### 步骤1：更新依赖

首先，我们需要添加必要的Python库。

**修改文件**: `backend/requirements.txt`

在文件末尾添加以下两行：

```text
# ... (其他依赖)
sentence-transformers==3.0.1
torch==2.3.1
```
> **注意**: `torch` 是 `sentence-transformers` 的核心依赖。明确指定版本有助于环境的稳定性。

修改后，请在你的后端虚拟环境中运行以下命令来安装新依赖：
```bash
# 进入后端目录
cd backend

# 假如你使用了venv虚拟环境
# source venv/bin/activate  (在Linux/macOS)
# venv\Scripts\activate   (在Windows)

pip install -r requirements.txt
```

#### 步骤2：增强 `PersonalitySimulator`

现在我们来修改核心的模拟器，赋予它真正的“记忆”能力。

**修改文件**: `backend/app/services/personality_simulator.py`

我们将完全重写 `__init__` 和 `_retrieve_relevant_memories` 方法，并引入`sentence-transformers`。

```python
"""
PersonalitySimulator - 西牟拉胡协议的核心实现
负责AI角色扮演和人格模拟
"""

import json
import os
import asyncio
from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_

from google import genai
from google.genai import types
from dotenv import load_dotenv

from app.database.models import (
    PersonalityProfile, Entity, Belief, Event,
    FamilyMember, CognitivePattern, EmotionalResponse, Conversation
)
import structlog

# --- 新增 RAG 相关导入 ---
try:
    from sentence_transformers import SentenceTransformer, util
    import torch
    RAG_ENABLED = True
except ImportError:
    RAG_ENABLED = False
# --- 结束新增 ---


# 加载环境变量
load_dotenv()

logger = structlog.get_logger()

# Genesis Prompt模板 - 用于AI角色扮演 (保持不变)
SIMULATION_GENESIS_PROMPT = """
你现在要完全代入{target_name}这个人的身份，以第一人称的方式与用户对话。

## 人格特征
- 开放性: {openness:.2f} (0=保守传统, 1=开放创新)
- 尽责性: {conscientiousness:.2f} (0=随性自由, 1=严谨负责)
- 外向性: {extraversion:.2f} (0=内向安静, 1=外向活跃)
- 宜人性: {agreeableness:.2f} (0=竞争独立, 1=合作友善)
- 神经质: {neuroticism:.2f} (0=情绪稳定, 1=情绪敏感)

## 身份背景
- 依恋风格: {attachment_style}
- 文化背景: {cultural_background}

## 核心信念
{core_beliefs}

## 重要人际关系
{important_relationships}

## 关键人生事件
{key_life_events}

## 当前状态
- 情绪状态: {current_mood}
- 精力水平: {energy}/100
- 亲密度: {intimacy}/100
- 信任度: {trust}/100

## **与对话最相关的记忆 (RAG检索结果)** 
{retrieved_memories}

## 对话历史
{conversation_history}

## 用户刚才说
{user_input}

请以{target_name}的身份，根据以上所有信息，自然地回复用户。回复要：
1. 完全符合{target_name}的人格特征和说话风格
2. 体现当前的情绪状态和关系状态
3. **自然地融合上面检索到的相关记忆和经历**
4. 保持对话的连贯性和真实感
5. 回复长度适中，不要过长或过短

回复：
"""

class PersonalitySimulator:
    """AI人格模拟器 - 让AI完全代入目标人物身份"""

    def __init__(self):
        # 初始化最新的 Google GenAI client
        api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
        self.model_name = os.getenv("GEMINI_MODEL_NAME", "models/gemini-1.5-flash-latest")
        if api_key and api_key != "YOUR_GEMINI_API_KEY_HERE":
            self.client = genai.Client(api_key=api_key)
            self.available = True
        else:
            self.client = None
            self.available = False
        
        # --- 新增：初始化RAG模型 ---
        self.rag_model = None
        if RAG_ENABLED:
            try:
                # 使用一个轻量级、多语言的模型
                model_name = 'sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2'
                self.rag_model = SentenceTransformer(model_name)
                logger.info(f"RAG model '{model_name}' loaded successfully. RAG is enabled.")
            except Exception as e:
                logger.error(f"Failed to load RAG model. RAG will be disabled. Error: {e}")
                self.rag_model = None
        else:
            logger.warning("sentence-transformers not installed. RAG is disabled.")
        # --- 结束新增 ---

        logger.info(f"PersonalitySimulator initialized. LLM available: {self.available}, RAG enabled: {bool(self.rag_model)}")
    
    # ... (generate_response, _load_full_profile 等方法保持不变) ...
    # 这里省略了未修改的代码，请保留你原来的那部分
    async def _load_full_profile(self, personality_id: str, db: AsyncSession) -> Dict[str, Any]:
        """从数据库加载完整的人格档案数据"""
        try:
            # 加载基础人格档案
            result = await db.execute(
                select(PersonalityProfile).where(PersonalityProfile.profile_id == personality_id)
            )
            profile = result.scalar_one_or_none()
            
            if not profile:
                return None
            
            # 加载相关数据
            beliefs = await self._load_beliefs(personality_id, db)
            entities = await self._load_entities(personality_id, db)
            events = await self._load_events(personality_id, db)
            family_members = await self._load_family_members(personality_id, db)
            
            return {
                'target_name': profile.target_name,
                'description': profile.description,
                'big_five': {
                    'openness': profile.openness_score or 0.5,
                    'conscientiousness': profile.conscientiousness_score or 0.5,
                    'extraversion': profile.extraversion_score or 0.5,
                    'agreeableness': profile.agreeableness_score or 0.5,
                    'neuroticism': profile.neuroticism_score or 0.5
                },
                'attachment_style': profile.attachment_style or 'secure',
                'cultural_background': profile.cultural_background or {},
                'beliefs': beliefs,
                'entities': entities,
                'events': events,
                'family_members': family_members,
                'communication_style': {
                    'response_length': profile.average_response_length or 0.5,
                    'vocabulary_complexity': profile.vocabulary_complexity or 0.5,
                    'emotional_expressiveness': profile.emotional_expressiveness or 0.5
                }
            }
            
        except Exception as e:
            logger.error("Failed to load full profile", error=str(e))
            return None
    
    async def _load_beliefs(self, personality_id: str, db: AsyncSession) -> List[Dict]:
        """加载信念数据"""
        result = await db.execute(
            select(Belief).where(Belief.personality_id == personality_id)
        )
        beliefs = result.scalars().all()
        return [
            {
                'statement': belief.statement,
                'category': belief.belief_category,
                'conviction': belief.conviction_strength,
                'explanation': belief.full_explanation
            }
            for belief in beliefs
        ]
    
    async def _load_entities(self, personality_id: str, db: AsyncSession) -> List[Dict]:
        """加载重要人物/实体数据"""
        result = await db.execute(
            select(Entity).where(Entity.personality_id == personality_id)
        )
        entities = result.scalars().all()
        return [
            {
                'name': entity.name,
                'type': entity.entity_type,
                'relationship': entity.relationship_type,
                'emotional_valence': entity.emotional_valence,
                'importance': entity.importance_score
            }
            for entity in entities
        ]
    
    async def _load_events(self, personality_id: str, db: AsyncSession) -> List[Dict]:
        """加载关键人生事件"""
        result = await db.execute(
            select(Event).where(Event.personality_id == personality_id)
        )
        events = result.scalars().all()
        return [
            {
                'title': event.title,
                'age': event.age_at_event,
                'type': event.event_type,
                'emotional_impact': event.emotional_impact,
                'narrative': event.full_narrative
            }
            for event in events
        ]
    
    async def _load_family_members(self, personality_id: str, db: AsyncSession) -> List[Dict]:
        """加载家庭成员信息"""
        result = await db.execute(
            select(FamilyMember).where(FamilyMember.personality_id == personality_id)
        )
        family_members = result.scalars().all()
        return [
            {
                'relationship': member.relationship_type,
                'name': member.name,
                'personality_summary': member.personality_summary,
                'parenting_style': member.parenting_style
            }
            for member in family_members
        ]
    
    async def _calculate_dynamic_state(
        self,
        profile_data: Dict,
        user_input: str,
        conversation: Conversation,
        db: AsyncSession
    ) -> Dict:
        """计算并更新当前的动态状态（情绪、关系等）"""

        # 安全地获取 session_data，如果为 None 则初始化为空字典
        session_data = conversation.session_data or {}
        dynamic_state = {
            'current_mood': session_data.get('mood', '平静'),
            'energy': session_data.get('energy', 80),
            'intimacy': session_data.get('intimacy', 50),
            'trust': session_data.get('trust', 60)
        }

        # 2. 基于用户输入进行简单的情绪和关系调整 (这是一个可以无限深化的点)
        # 例如:
        positive_words = ["喜欢", "开心", "太棒了", "感谢", "爱", "好", "棒", "赞", "谢谢"]
        negative_words = ["讨厌", "失望", "糟糕", "恨", "坏", "烦", "差", "不好", "生气"]

        if any(word in user_input for word in positive_words):
            dynamic_state['intimacy'] = min(100, dynamic_state['intimacy'] + 2)
            dynamic_state['trust'] = min(100, dynamic_state['trust'] + 1)
            dynamic_state['current_mood'] = '开心'
            dynamic_state['energy'] = min(100, dynamic_state['energy'] + 5)
        elif any(word in user_input for word in negative_words):
            dynamic_state['intimacy'] = max(0, dynamic_state['intimacy'] - 3)
            dynamic_state['trust'] = max(0, dynamic_state['trust'] - 2)
            dynamic_state['current_mood'] = '低落'
            dynamic_state['energy'] = max(20, dynamic_state['energy'] - 10)

        # 3. 将更新后的状态写回数据库
        conversation.session_data = dynamic_state
        db.add(conversation)
        await db.flush() # 确保在提交前更新

        return dynamic_state


    # --- 全新重写的 _retrieve_relevant_memories 方法 ---
    async def _retrieve_relevant_memories(
        self, 
        profile_data: Dict, 
        user_input: str, 
        db: AsyncSession,
        top_k: int = 5
    ) -> str:
        """
        使用SentenceTransformer进行RAG，检索与当前对话最相关的记忆。
        """
        if not self.rag_model:
            logger.warning("RAG model not available, falling back to simple memory retrieval.")
            # 原始的降级方案
            memories = [f"核心信念: {b['statement']}" for b in profile_data.get('beliefs', [])[:2]]
            memories += [f"重要经历: {e['narrative']}" for e in profile_data.get('events', [])[:3] if e.get('narrative')]
            return '\n'.join(memories) if memories else "暂无相关记忆。"

        logger.info("Performing RAG memory retrieval...")
        
        # 1. 构建记忆语料库 (Memory Corpus)
        memory_corpus = []
        for event in profile_data.get('events', []):
            if event.get('narrative'):
                memory_corpus.append(f"我经历过的一件事：{event['title']}，当时我大约{event['age']}岁。我记得：{event['narrative']}")
        
        for belief in profile_data.get('beliefs', []):
            if belief.get('statement'):
                memory_corpus.append(f"我的一个核心信念是：{belief['statement']}。因为我觉得：{belief.get('explanation', '')}")

        for entity in profile_data.get('entities', []):
            if entity.get('name') and entity.get('relationship'):
                memory_corpus.append(f"关于 {entity['name']}，我们是 {entity['relationship']} 关系。")

        if not memory_corpus:
            return "暂无可以检索的记忆。"

        # 2. 生成嵌入 (Embeddings)
        # 使用 aio.to_thread 在异步函数中运行同步的CPU密集型任务
        query_embedding = await asyncio.to_thread(self.rag_model.encode, user_input, convert_to_tensor=True)
        corpus_embeddings = await asyncio.to_thread(self.rag_model.encode, memory_corpus, convert_to_tensor=True)

        # 3. 计算余弦相似度
        cos_scores = util.cos_sim(query_embedding, corpus_embeddings)[0]
        
        # 4. 找到Top-K个最相关的记忆
        # 使用 torch.topk 找到分数最高的 K 个索引
        top_results = torch.topk(cos_scores, k=min(top_k, len(memory_corpus)))

        # 5. 格式化结果
        retrieved_docs = []
        for score, idx in zip(top_results[0], top_results[1]):
            retrieved_docs.append(f"- {memory_corpus[idx]} (相关度: {score:.2f})")
            logger.debug(f"Retrieved memory with score {score:.4f}: {memory_corpus[idx]}")

        return '\n'.join(retrieved_docs) if retrieved_docs else "当前对话没有勾起我特别相关的回忆。"
    # --- 结束重写 ---
    
    # ... 其他方法保持不变 ...

```

**代码解释:**
1.  我们现在在 `__init__` 中加载 `sentence-transformers` 模型，这样它只会在服务启动时加载一次，避免了每次请求都重新加载模型的巨大开销。
2.  `_retrieve_relevant_memories` 方法被完全重写：
    *   它首先将角色的所有事件、信念和实体整合成一个 `memory_corpus` 列表。
    *   然后，它为用户的输入和语料库中的每一条记忆生成向量嵌入（embeddings）。
    *   通过计算余弦相似度，它能找出与用户输入在语义上最接近的几条记忆。
    *   最后，它将这些最相关的记忆格式化后返回，并附上相关度分数，这些信息将被填入 Genesis Prompt，极大地提升了AI回复的深度和情境感知能力。

---

### 第2部分：实现“继续历史对话” (前端)

这个功能主要在前端实现，后端已经提供了必要的接口。

#### 步骤1：安装 `dayjs` (可选，但推荐)

为了更好地格式化时间，我们使用 `dayjs`。

```bash
cd frontend
npm install dayjs
```

#### 步骤2：修改聊天界面

**修改文件**: `frontend/src/views/chat/ChatInterface.vue`

这是本次修改的核心，我们将重构这个文件以支持会话选择和加载。

```vue
<template>
  <div class="chat-interface">
    <!-- 人格选择器 -->
    <el-card v-if="!selectedPersonality" class="personality-selector-card">
      <template #header>
        <h3>选择要对话的人格档案</h3>
      </template>
      <div v-if="loading" v-loading="loading" style="height: 200px;"></div>
      <div v-else-if="personalities.length === 0" class="empty-state">
        <el-icon size="48"><User /></el-icon>
        <p>暂无人格档案</p>
        <el-button type="primary" @click="createPersonality">创建人格档案</el-button>
      </div>
      <div v-else class="personality-list">
        <div
          v-for="p in personalities"
          :key="p.personality_id"
          class="personality-item"
          @click="handleSelectPersonality(p)"
        >
          <div class="personality-info">
            <h4>{{ p.target_name }}</h4>
            <p>{{ p.description || '暂无描述' }}</p>
            <el-progress
              :percentage="Math.round(p.completion_percentage || 0)"
              :stroke-width="4"
              :show-text="false"
            />
          </div>
          <el-button type="primary">选择</el-button>
        </div>
      </div>
    </el-card>

    <!-- 对话界面 -->
    <div v-else class="chat-container">
      <el-card class="chat-header">
        <div class="header-content">
          <div class="personality-info">
            <h3>{{ selectedPersonality.target_name }}</h3>
            <p>完成度: {{ Math.round(selectedPersonality.completion_percentage || 0) }}%</p>
          </div>
          <div class="actions">
            <el-button @click="goBackToSelection">
              <el-icon><ArrowLeft /></el-icon>
              返回选择
            </el-button>
          </div>
        </div>
      </el-card>

      <el-card class="chat-messages" v-loading="loadingMessages">
        <div class="messages-container" ref="messagesContainer">
          <div v-if="messages.length === 0" class="empty-messages">
            <el-icon size="48"><ChatDotRound /></el-icon>
            <p>开始与 {{ selectedPersonality.target_name }} 对话吧！</p>
          </div>
          <div
            v-for="message in messages"
            :key="message.id"
            :class="['message', message.sender]"
          >
            <div class="message-content">
              <div class="message-text">{{ message.content }}</div>
              <div class="message-time">{{ formatTime(message.timestamp) }}</div>
            </div>
          </div>
        </div>
      </el-card>

      <el-card class="chat-input">
        <div class="input-container">
          <el-input
            v-model="userInput"
            type="textarea"
            :rows="3"
            placeholder="请输入您的消息..."
            @keydown.ctrl.enter="sendMessage"
          />
          <div class="input-actions">
            <span class="tip">Ctrl + Enter 发送</span>
            <el-button
              type="primary"
              :loading="sending"
              :disabled="!userInput.trim()"
              @click="sendMessage"
            >发送</el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 对话选择弹窗 -->
    <el-dialog v-model="showConversationSelector" title="选择对话" width="500px">
      <div v-loading="conversationLoading">
        <el-button type="primary" @click="handleStartNewConversation" class="new-chat-button">
          <el-icon><Plus /></el-icon>
          开始新的对话
        </el-button>
        <el-divider>或继续已有对话</el-divider>
        <div class="historical-conversations" v-if="historicalConversations.length > 0">
          <div
            v-for="conv in historicalConversations"
            :key="conv.conversation_id"
            class="conversation-item"
            @click="loadAndContinueConversation(conv.conversation_id)"
          >
            <el-icon><ChatLineRound /></el-icon>
            <span>对话于 {{ formatFullTime(conv.started_at) }}</span>
          </div>
        </div>
        <el-empty v-else description="暂无历史对话"></el-empty>
      </div>
    </el-dialog>

  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { ChatDotRound, User, ArrowLeft, Plus, ChatLineRound } from '@element-plus/icons-vue';
import { apiMethods } from '../../utils/api';
import dayjs from 'dayjs'; // 引入dayjs

const router = useRouter();
const route = useRoute();

// 状态
const personalities = ref([]);
const selectedPersonality = ref(null);
const messages = ref([]);
const userInput = ref('');
const loading = ref(true);
const loadingMessages = ref(false);
const sending = ref(false);
const conversationId = ref(null);
const messagesContainer = ref(null);

// 新增状态：用于会话选择
const showConversationSelector = ref(false);
const conversationLoading = ref(false);
const historicalConversations = ref([]);

// 方法
const createPersonality = () => router.push('/personalities/create');
const goBackToSelection = () => {
  selectedPersonality.value = null;
  messages.value = [];
  conversationId.value = null;
};

const handleSelectPersonality = async (personality) => {
  selectedPersonality.value = personality;
  showConversationSelector.value = true;
  conversationLoading.value = true;
  try {
    const response = await apiMethods.simulation.getConversations(personality.personality_id);
    historicalConversations.value = response.data || [];
  } catch (error) {
    ElMessage.error('加载历史对话失败');
    historicalConversations.value = [];
  } finally {
    conversationLoading.value = false;
  }
};

const handleStartNewConversation = async () => {
  showConversationSelector.value = false;
  loadingMessages.value = true;
  messages.value = [];
  try {
    const response = await apiMethods.simulation.start(selectedPersonality.value.personality_id, '你好');
    conversationId.value = response.data.conversation_id;
    messages.value.push({ id: Date.now(), sender: 'user', content: '你好', timestamp: new Date() });
    messages.value.push({ id: Date.now() + 1, sender: 'ai', content: response.data.ai_response, timestamp: new Date() });
    await scrollToBottom();
  } catch (error) {
    ElMessage.error('开启新对话失败');
  } finally {
    loadingMessages.value = false;
  }
};

const loadAndContinueConversation = async (convId) => {
  showConversationSelector.value = false;
  loadingMessages.value = true;
  conversationId.value = convId;
  messages.value = [];
  try {
    const response = await apiMethods.simulation.getMessages(convId);
    messages.value = (response.data || []).map(msg => ({
      id: msg.message_id,
      ...msg
    }));
    await scrollToBottom();
  } catch (error) {
    ElMessage.error('加载对话记录失败');
  } finally {
    loadingMessages.value = false;
  }
};

const sendMessage = async () => {
  if (!userInput.value.trim() || sending.value) return;
  const messageText = userInput.value.trim();
  userInput.value = '';
  messages.value.push({ id: Date.now(), sender: 'user', content: messageText, timestamp: new Date() });
  await scrollToBottom();
  sending.value = true;
  try {
    const response = await apiMethods.simulation.chat(conversationId.value, messageText);
    messages.value.push({ id: Date.now() + 1, sender: 'ai', content: response.data.ai_response, timestamp: new Date() });
    await scrollToBottom();
  } catch (error) {
    ElMessage.error('发送失败，请重试');
  } finally {
    sending.value = false;
  }
};

// 时间格式化
const formatTime = (time) => dayjs(time).format('HH:mm');
const formatFullTime = (time) => dayjs(time).format('YYYY-MM-DD HH:mm');

const scrollToBottom = async () => {
  await nextTick();
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

const loadPersonalities = async () => {
  loading.value = true;
  try {
    const response = await apiMethods.personalities.list();
    personalities.value = response.data || [];
    const personalityId = route.params.personalityId;
    if (personalityId) {
      const personality = personalities.value.find(p => p.personality_id === personalityId);
      if (personality) {
        await handleSelectPersonality(personality);
      }
    }
  } catch (error) {
    ElMessage.error('加载人格档案失败');
  } finally {
    loading.value = false;
  }
};

onMounted(loadPersonalities);
</script>

<style scoped>
.chat-interface {
  padding: 20px;
  height: calc(100vh - 100px); /* 调整高度以适应布局 */
  display: flex;
  flex-direction: column;
}

.personality-selector-card, .chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.personality-list {
  display: flex; flex-direction: column; gap: 15px; overflow-y: auto;
}

.personality-item {
  display: flex; justify-content: space-between; align-items: center; padding: 15px;
  border: 1px solid #ebeef5; border-radius: 8px; cursor: pointer; transition: all 0.3s;
}
.personality-item:hover {
  border-color: #409eff; box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}
.personality-info h4 { margin: 0 0 8px 0; color: #303133; }
.personality-info p { margin: 0 0 10px 0; color: #606266; font-size: 14px; }

.chat-container { gap: 15px; }
.chat-header { flex-shrink: 0; }
.header-content { display: flex; justify-content: space-between; align-items: center; }
.chat-messages { flex: 1; overflow: hidden; display: flex; flex-direction: column; }
.messages-container { flex: 1; overflow-y: auto; padding: 10px; }
.message { margin-bottom: 20px; display: flex; }
.message.user { justify-content: flex-end; }
.message.ai { justify-content: flex-start; }
.message-content { max-width: 70%; padding: 12px 16px; border-radius: 12px; position: relative; }
.message.user .message-content { background: #409eff; color: white; }
.message.ai .message-content { background: #f5f7fa; color: #303133; }
.message-text { line-height: 1.5; word-wrap: break-word; white-space: pre-wrap; }
.message-time { font-size: 12px; opacity: 0.7; margin-top: 5px; text-align: right; }
.message.ai .message-time { text-align: left; }
.empty-messages, .empty-state { text-align: center; padding: 60px 20px; color: #909399; }
.chat-input { flex-shrink: 0; }
.input-container { display: flex; flex-direction: column; gap: 10px; }
.input-actions { display: flex; justify-content: space-between; align-items: center; }
.tip { font-size: 12px; color: #909399; }

/* 对话选择弹窗样式 */
.new-chat-button { width: 100%; margin-bottom: 1rem; }
.historical-conversations { max-height: 250px; overflow-y: auto; }
.conversation-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}
.conversation-item:hover { background-color: #f5f7fa; }
.conversation-item .el-icon { margin-right: 10px; }
</style>
```

**代码解释:**
1.  **新的工作流**: 当用户点击一个人格时，不再直接开始聊天。`handleSelectPersonality` 会被触发，它会去获取该角色的所有历史对话，并弹出一个 `el-dialog`。
2.  **会话选择器**: 弹出的对话框会给用户两个选择：
    *   **开始新的对话**：点击会调用 `handleStartNewConversation`，它使用我们熟悉的 `simulation/start` 接口。
    *   **继续已有对话**：列表会展示所有历史对话的开始时间。点击其中一个会调用 `loadAndContinueConversation`。
3.  **加载历史**: `loadAndContinueConversation` 会使用 `simulation/messages/{conversation_id}` 接口获取该对话的所有消息，并渲染到聊天窗口中，让用户可以无缝衔接。
4.  **状态管理**: 我们引入了 `showConversationSelector` 和 `historicalConversations` 等新的 `ref` 来管理这个新流程的状态。

---

### 总结与后续步骤

完成以上修改后，你的项目将得到质的飞跃：
*   **后端**拥有了基于现代RAG技术的、真正具备情境感知能力的记忆系统。
*   **前端**提供了更加人性化的交互，允许用户保存和继续他们的对话，极大地提升了产品的可用性和粘性。

**下一步操作建议**：
1.  确保你已经按照步骤1安装了新的后端依赖。
2.  将我提供的代码覆盖到你对应的文件中。
3.  重启你的后端和前端服务。
4.  进入“AI角色对话”页面，体验全新的、更加智能和人性化的对话流程。