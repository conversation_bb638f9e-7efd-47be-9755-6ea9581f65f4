import uuid
from datetime import datetime
from sqlalchemy import Column, String, Text, Float, SMALLINT, ForeignKey, Enum, JSON, DateTime, <PERSON>olean, Integer
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.dialects.sqlite import <PERSON><PERSON><PERSON> as SQLiteJSON
from sqlalchemy.orm import declarative_base, relationship
from sqlalchemy.sql import func
import enum

Base = declarative_base()

class PersonalityDimension(enum.Enum):
    OPENNESS = "openness"
    CONSCIENTIOUSNESS = "conscientiousness"
    EXTRAVERSION = "extraversion"
    AGREEABLENESS = "agreeableness"
    NEUROTICISM = "neuroticism"

class CognitiveStyle(enum.Enum):
    ANALYTICAL = "analytical"
    INTUITIVE = "intuitive"
    SYSTEMATIC = "systematic"
    CREATIVE = "creative"

class EmotionalState(enum.Enum):
    JOY = "joy"
    SADNESS = "sadness"
    ANGER = "anger"
    FEAR = "fear"
    SURPRISE = "surprise"
    DISGUST = "disgust"
    NEUTRAL = "neutral"

class User(Base):
    __tablename__ = 'users'
    user_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(255), unique=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    is_active = Column(Boolean, default=True)
    
    # Relationships
    personalities = relationship("PersonalityProfile", back_populates="user")
    conversations = relationship("Conversation", back_populates="user")

class PersonalityProfile(Base):
    __tablename__ = 'personality_profiles'
    profile_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.user_id'), nullable=False)
    target_name = Column(String(255), nullable=False)  # 被复刻人的名字
    description = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completion_percentage = Column(Float, default=0.0)  # 复刻完成度
    
    # Big Five personality scores
    openness_score = Column(Float, default=0.5)
    conscientiousness_score = Column(Float, default=0.5)
    extraversion_score = Column(Float, default=0.5)
    agreeableness_score = Column(Float, default=0.5)
    neuroticism_score = Column(Float, default=0.5)
    
    # Cognitive patterns
    dominant_cognitive_style = Column(Enum(CognitiveStyle))
    decision_making_speed = Column(Float)  # 0-1, slow to fast
    risk_tolerance = Column(Float)  # 0-1, risk-averse to risk-seeking
    
    # Communication patterns
    average_response_length = Column(Float)
    vocabulary_complexity = Column(Float)  # 0-1, simple to complex
    emotional_expressiveness = Column(Float)  # 0-1, reserved to expressive

    # 身份同心圆核心数据
    attachment_style = Column(String(50), default='secure')  # 'secure', 'anxious', 'avoidant', 'disorganized'
    cultural_background = Column(JSON, nullable=True)  # {"ethnicity": "汉族", "region": "华北", "generation": "90后"}

    user = relationship("User", back_populates="personalities")
    entities = relationship("Entity", back_populates="personality")
    beliefs = relationship("Belief", back_populates="personality")
    events = relationship("Event", back_populates="personality")
    conversations = relationship("Conversation", back_populates="personality")
    family_members = relationship("FamilyMember", back_populates="personality")

class Entity(Base):
    __tablename__ = 'entities'
    entity_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    personality_id = Column(UUID(as_uuid=True), ForeignKey('personality_profiles.profile_id'), nullable=False)
    name = Column(String(255), nullable=False, index=True)
    entity_type = Column(String(50), nullable=False)  # person, place, concept, etc.
    relationship_type = Column(String(100))  # family, friend, colleague, etc.
    emotional_valence = Column(Float)  # -1 to 1, negative to positive
    importance_score = Column(Float)  # 0-1
    profile = Column(JSON, nullable=True)

    personality = relationship("PersonalityProfile", back_populates="entities")

class Belief(Base):
    __tablename__ = 'beliefs'
    belief_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    personality_id = Column(UUID(as_uuid=True), ForeignKey('personality_profiles.profile_id'), nullable=False)
    statement = Column(Text, nullable=False)
    belief_category = Column(String(50), nullable=False)  # moral, political, personal, etc.
    conviction_strength = Column(Float, nullable=False)  # 0-1
    flexibility_score = Column(Float)  # 0-1, rigid to flexible
    origin_context = Column(Text)  # How this belief was formed
    full_explanation = Column(Text)

    # --- 新增字段：记忆显化度和遗忘曲线 ---
    salience = Column(Float, default=0.5, nullable=False)  # 记忆显化度 (深刻度) 0-1
    last_recalled_at = Column(DateTime(timezone=True), server_default=func.now())  # 上次回忆时间
    source = Column(String(50), default='user_provided')  # 记忆来源: user_provided, virtual, generated
    # --- 结束新增字段 ---

    personality = relationship("PersonalityProfile", back_populates="beliefs")

class Event(Base):
    __tablename__ = 'events'
    event_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    personality_id = Column(UUID(as_uuid=True), ForeignKey('personality_profiles.profile_id'), nullable=False)
    title = Column(String(255), nullable=False)
    age_at_event = Column(SMALLINT)
    life_stage = Column(String(50))  # childhood, adolescence, young_adult, etc.
    event_type = Column(String(50))  # achievement, trauma, relationship, etc.
    emotional_impact = Column(Float)  # -1 to 1
    centrality_score = Column(Float, nullable=False)  # 0-1
    memory_vividness = Column(Float)  # 0-1
    lessons_learned = Column(JSON)  # 改为JSON格式以兼容SQLite
    full_narrative = Column(Text)

    # --- 新增字段：记忆显化度和遗忘曲线 ---
    salience = Column(Float, default=0.5, nullable=False)  # 记忆显化度 (深刻度) 0-1
    last_recalled_at = Column(DateTime(timezone=True), server_default=func.now())  # 上次回忆时间
    source = Column(String(50), default='user_provided')  # 记忆来源: user_provided, virtual, generated
    # --- 结束新增字段 ---

    personality = relationship("PersonalityProfile", back_populates="events")

class CognitivePattern(Base):
    __tablename__ = 'cognitive_patterns'
    pattern_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    personality_id = Column(UUID(as_uuid=True), ForeignKey('personality_profiles.profile_id'), nullable=False)
    pattern_type = Column(String(50), nullable=False)  # decision_tree, reaction_pattern, etc.
    trigger_conditions = Column(JSON)  # What triggers this pattern
    typical_response = Column(Text)
    confidence_level = Column(Float)  # 0-1
    frequency_observed = Column(Integer, default=1)
    context_tags = Column(JSON)  # 改为JSON格式以兼容SQLite

class LanguagePattern(Base):
    __tablename__ = 'language_patterns'
    pattern_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    personality_id = Column(UUID(as_uuid=True), ForeignKey('personality_profiles.profile_id'), nullable=False)
    pattern_type = Column(String(50))  # vocabulary, syntax, style, etc.
    pattern_data = Column(JSON)  # Specific pattern details
    frequency = Column(Float)  # How often this pattern appears
    context = Column(String(100))  # formal, casual, emotional, etc.

class EmotionalResponse(Base):
    __tablename__ = 'emotional_responses'
    response_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    personality_id = Column(UUID(as_uuid=True), ForeignKey('personality_profiles.profile_id'), nullable=False)
    trigger_situation = Column(Text, nullable=False)
    primary_emotion = Column(Enum(EmotionalState), nullable=False)
    intensity = Column(Float)  # 0-1
    duration_pattern = Column(String(50))  # quick, moderate, prolonged
    coping_mechanism = Column(Text)
    typical_expression = Column(Text)  # How they express this emotion

class Conversation(Base):
    __tablename__ = 'conversations'
    conversation_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.user_id'), nullable=False)
    personality_id = Column(UUID(as_uuid=True), ForeignKey('personality_profiles.profile_id'), nullable=False)
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    ended_at = Column(DateTime(timezone=True))
    session_data = Column(JSON)  # Store session state
    
    user = relationship("User", back_populates="conversations")
    personality = relationship("PersonalityProfile", back_populates="conversations")
    messages = relationship("Message", back_populates="conversation")

class FamilyMember(Base):
    __tablename__ = 'family_members'
    member_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    personality_id = Column(UUID(as_uuid=True), ForeignKey('personality_profiles.profile_id'), nullable=False)
    relationship_type = Column(String(50), nullable=False)  # "父亲", "母亲", "兄长"
    name = Column(String(255))
    personality_summary = Column(JSON)  # 简化的人格模型, {"extraversion": 0.8, "agreeableness": 0.3}
    parenting_style = Column(String(50))  # "权威型", "专断型" 等, 仅父母有

    personality = relationship("PersonalityProfile", back_populates="family_members")

class Message(Base):
    __tablename__ = 'messages'
    message_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    conversation_id = Column(UUID(as_uuid=True), ForeignKey('conversations.conversation_id'), nullable=False)
    sender = Column(String(20), nullable=False)  # 'user' or 'ai'
    content = Column(Text, nullable=False)
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    analysis_data = Column(JSON)  # Store analysis results

    conversation = relationship("Conversation", back_populates="messages")
