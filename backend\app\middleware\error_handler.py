"""
统一错误处理中间件
提供全局异常捕获和标准化错误响应
"""

import traceback
from typing import Dict, Any
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.middleware.base import BaseHTTPMiddleware
import structlog

logger = structlog.get_logger()

class ErrorHandlerMiddleware(BaseHTTPMiddleware):
    """全局错误处理中间件"""
    
    async def dispatch(self, request: Request, call_next):
        try:
            response = await call_next(request)
            return response
        except HTTPException as e:
            # FastAPI HTTP异常，直接传递
            raise e
        except RequestValidationError as e:
            # 请求验证错误
            logger.warning(
                "Request validation error",
                path=request.url.path,
                method=request.method,
                errors=e.errors()
            )
            return JSONResponse(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                content={
                    "error": "请求参数验证失败",
                    "detail": e.errors(),
                    "type": "validation_error"
                }
            )
        except Exception as e:
            # 未捕获的异常
            error_id = self._generate_error_id()
            logger.error(
                "Unhandled exception",
                error_id=error_id,
                path=request.url.path,
                method=request.method,
                error_type=type(e).__name__,
                error_message=str(e),
                traceback=traceback.format_exc()
            )
            
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "error": "服务器内部错误",
                    "error_id": error_id,
                    "type": "internal_error"
                }
            )
    
    def _generate_error_id(self) -> str:
        """生成错误ID用于追踪"""
        import uuid
        return str(uuid.uuid4())[:8]

class CustomHTTPException(HTTPException):
    """自定义HTTP异常，支持更多错误信息"""
    
    def __init__(
        self,
        status_code: int,
        detail: str,
        error_code: str = None,
        error_data: Dict[str, Any] = None
    ):
        super().__init__(status_code=status_code, detail=detail)
        self.error_code = error_code
        self.error_data = error_data or {}

# 预定义的业务异常
class BusinessException(CustomHTTPException):
    """业务逻辑异常"""
    pass

class AuthenticationException(CustomHTTPException):
    """认证异常"""
    def __init__(self, detail: str = "认证失败"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            error_code="AUTH_FAILED"
        )

class AuthorizationException(CustomHTTPException):
    """授权异常"""
    def __init__(self, detail: str = "权限不足"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail,
            error_code="PERMISSION_DENIED"
        )

class ResourceNotFoundException(CustomHTTPException):
    """资源未找到异常"""
    def __init__(self, resource: str = "资源"):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"{resource}未找到",
            error_code="RESOURCE_NOT_FOUND"
        )

class ValidationException(CustomHTTPException):
    """数据验证异常"""
    def __init__(self, detail: str, field: str = None):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=detail,
            error_code="VALIDATION_ERROR",
            error_data={"field": field} if field else {}
        )

class ExternalServiceException(CustomHTTPException):
    """外部服务异常"""
    def __init__(self, service: str, detail: str = None):
        super().__init__(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=detail or f"{service}服务暂时不可用",
            error_code="EXTERNAL_SERVICE_ERROR",
            error_data={"service": service}
        )

# 错误处理工具函数
def handle_database_error(e: Exception, operation: str = "数据库操作") -> CustomHTTPException:
    """处理数据库错误"""
    logger.error(f"Database error during {operation}", error=str(e))
    
    if "connection" in str(e).lower():
        return ExternalServiceException("数据库", "数据库连接失败")
    elif "constraint" in str(e).lower():
        return ValidationException("数据约束违反，请检查输入数据")
    else:
        return CustomHTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"{operation}失败",
            error_code="DATABASE_ERROR"
        )

def handle_ai_service_error(e: Exception, service: str = "AI服务") -> CustomHTTPException:
    """处理AI服务错误"""
    logger.error(f"AI service error: {service}", error=str(e))
    
    if "api key" in str(e).lower() or "authentication" in str(e).lower():
        return ExternalServiceException(service, "API密钥配置错误")
    elif "quota" in str(e).lower() or "limit" in str(e).lower():
        return ExternalServiceException(service, "API配额已用完")
    elif "timeout" in str(e).lower():
        return ExternalServiceException(service, "服务响应超时")
    else:
        return ExternalServiceException(service, f"{service}暂时不可用")

# 装饰器：自动错误处理
def handle_errors(operation_name: str = "操作"):
    """错误处理装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except CustomHTTPException:
                # 自定义异常直接抛出
                raise
            except Exception as e:
                logger.error(f"Error in {operation_name}", error=str(e))
                raise CustomHTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"{operation_name}失败",
                    error_code="OPERATION_ERROR"
                )
        return wrapper
    return decorator
